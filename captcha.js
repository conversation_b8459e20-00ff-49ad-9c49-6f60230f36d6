const axios = require('axios');

class CaptchaHandler {
    constructor() {
        this.yesCaptchaKey = 'yescaptcha密钥';
        this.defaultSiteKey = '0x4AAAAAAABkMYinukE_rfkN';
        this.recaptchaSiteKey = '6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1';
    }

    // 处理Cloudflare Turnstile
    async handleTurnstile(page, websiteURL, siteKey) {
        try {
            console.log('[CAPTCHA] 处理Turnstile验证码...');
            console.log(`[CAPTCHA] URL: ${websiteURL}`);
            console.log(`[CAPTCHA] SiteKey: ${siteKey}`);
            
            const taskId = await this.createTurnstileTask(websiteURL, siteKey);
            if (!taskId) {
                console.log('[CAPTCHA] Turnstile任务创建失败');
                return false;
            }
            
            const token = await this.getCaptchaResult(taskId);
            if (!token) {
                console.log('[CAPTCHA] Turnstile token获取失败');
                return false;
            }
            
            await this.injectTurnstileToken(page, token);
            console.log('[CAPTCHA] ✅ Turnstile处理完成');
            return true;
            
        } catch (error) {
            console.log(`[CAPTCHA] Turnstile处理失败: ${error.message}`);
            return false;
        }
    }

    // 处理reCAPTCHA Enterprise
    async handleRecaptchaEnterprise(page) {
        try {
            console.log('[CAPTCHA] 处理reCAPTCHA Enterprise...');
            
            // 等待reCAPTCHA脚本加载
            const hasRecaptcha = await page.evaluate(() => {
                return !!(window.grecaptcha && window.grecaptcha.enterprise);
            });
            
            if (!hasRecaptcha) {
                console.log('[CAPTCHA] 未检测到reCAPTCHA Enterprise');
                return false;
            }
            
            // 执行reCAPTCHA
            const token = await page.evaluate(async (siteKey) => {
                try {
                    if (window.grecaptcha && window.grecaptcha.enterprise) {
                        const token = await window.grecaptcha.enterprise.execute(siteKey, { 
                            action: 'submit' 
                        });
                        return token;
                    }
                    return null;
                } catch (error) {
                    console.error('reCAPTCHA执行失败:', error);
                    return null;
                }
            }, this.recaptchaSiteKey);
            
            if (token) {
                await this.injectRecaptchaToken(page, token);
                console.log('[CAPTCHA] ✅ reCAPTCHA Enterprise处理完成');
                return true;
            } else {
                console.log('[CAPTCHA] reCAPTCHA Enterprise token获取失败');
                return false;
            }
            
        } catch (error) {
            console.log(`[CAPTCHA] reCAPTCHA Enterprise处理失败: ${error.message}`);
            return false;
        }
    }

    // 创建Turnstile任务
    async createTurnstileTask(websiteURL, siteKey) {
        try {
            const response = await axios.post('https://api.yescaptcha.com/createTask', {
                clientKey: this.yesCaptchaKey,
                task: {
                    type: 'TurnstileTaskProxyless',
                    websiteURL: websiteURL,
                    websiteKey: siteKey
                }
            });
            
            if (response.data.errorId === 0) {
                console.log(`[CAPTCHA] 任务创建成功: ${response.data.taskId}`);
                return response.data.taskId;
            } else {
                console.log(`[CAPTCHA] 任务创建失败: ${response.data.errorDescription}`);
                return null;
            }
        } catch (error) {
            console.log(`[CAPTCHA] 创建任务失败: ${error.message}`);
            return null;
        }
    }

    // 获取验证码结果
    async getCaptchaResult(taskId) {
        try {
            const startTime = Date.now();
            let checkCount = 0;
            
            while (Date.now() - startTime < 80000) {
                checkCount++;
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                
                if (checkCount % 20 === 1) {
                    console.log(`[CAPTCHA] 处理中... (${elapsed}s)`);
                }
                
                const response = await axios.post('https://api.yescaptcha.com/getTaskResult', {
                    clientKey: this.yesCaptchaKey,
                    taskId: taskId
                });
                
                if (response.data.errorId > 0) {
                    console.log(`[CAPTCHA] 获取结果失败: ${response.data.errorDescription}`);
                    return null;
                }
                
                if (response.data.status === 'ready') {
                    console.log('[CAPTCHA] ✅ 验证码解决成功');
                    return response.data.solution.token;
                }
                
                if (response.data.status === 'processing') {
                    await this.wait(3000);
                }
            }
            
            console.log('[CAPTCHA] ❌ 验证码处理超时');
            return null;
        } catch (error) {
            console.log(`[CAPTCHA] 获取结果失败: ${error.message}`);
            return null;
        }
    }

    // 注入Turnstile token
    async injectTurnstileToken(page, token) {
        try {
            await page.evaluate((token) => {
                // 注入到各种可能的输入框
                const selectors = [
                    'input[name="cf-turnstile-response"]',
                    'input[name*="turnstile"]',
                    'input[name*="captcha"]'
                ];
                
                selectors.forEach(selector => {
                    const inputs = document.querySelectorAll(selector);
                    inputs.forEach(input => {
                        input.value = token;
                    });
                });
                
                // 触发Turnstile回调
                if (window.turnstile && window.turnstile.render) {
                    const widgets = document.querySelectorAll('[data-sitekey]');
                    widgets.forEach(widget => {
                        if (widget._turnstileCallback) {
                            widget._turnstileCallback(token);
                        }
                    });
                }
                
                // 存储到全局变量
                window.__turnstileToken = token;
                
                return true;
            }, token);
            
            await this.wait(2000);
            return true;
            
        } catch (error) {
            console.log(`[CAPTCHA] 注入Turnstile token失败: ${error.message}`);
            return false;
        }
    }

    // 注入reCAPTCHA token
    async injectRecaptchaToken(page, token) {
        try {
            await page.evaluate((token) => {
                // 注入到reCAPTCHA响应字段
                let input = document.querySelector('input[name="g-recaptcha-response"]');
                if (!input) {
                    input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'g-recaptcha-response';
                    const form = document.querySelector('form');
                    if (form) {
                        form.appendChild(input);
                    }
                }
                input.value = token;
                
                // 存储到全局变量
                window.__recaptchaToken = token;
                
                return true;
            }, token);
            
            await this.wait(1000);
            return true;
            
        } catch (error) {
            console.log(`[CAPTCHA] 注入reCAPTCHA token失败: ${error.message}`);
            return false;
        }
    }

    // 辅助方法
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = CaptchaHandler;

